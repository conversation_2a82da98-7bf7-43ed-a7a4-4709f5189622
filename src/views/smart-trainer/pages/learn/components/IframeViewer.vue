<template>
    <div class="iframe-viewer">
        <!-- 顶部导航栏 -->
        <div class="iframe-header">
            <div class="header-left">
                <button class="back-btn" @click="handleBack">
                    <i class="pi pi-arrow-left"></i>
                    <span>返回</span>
                </button>
            </div>
            <div class="header-center">
                <span class="page-title">{{ title || '内容预览' }}</span>
            </div>
            <div class="header-right">
                <button class="refresh-btn" @click="handleRefresh" :disabled="loading">
                    <i class="pi pi-refresh" :class="{ 'spinning': loading }"></i>
                </button>
            </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
            <div class="loading-spinner">
                <i class="pi pi-spin pi-spinner"></i>
                <span>加载中...</span>
            </div>
        </div>

        <!-- 错误状态 -->
        <div v-if="error" class="error-container">
            <div class="error-content">
                <i class="pi pi-exclamation-triangle"></i>
                <h3>加载失败</h3>
                <p>{{ error }}</p>
                <button class="retry-btn" @click="handleRetry">重试</button>
            </div>
        </div>

        <!-- iframe 容器 -->
        <div v-show="!loading && !error" class="iframe-container">
            <iframe
                ref="iframeRef"
                :src="url"
                class="content-iframe"
                frameborder="0"
                @load="handleIframeLoad"
                @error="handleIframeError"
            ></iframe>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';

// Props
const props = defineProps({
    url: {
        type: String,
        required: true
    },
    title: {
        type: String,
        default: ''
    },
    showHeader: {
        type: Boolean,
        default: true
    }
});

// Emits
const emit = defineEmits(['back', 'load', 'error']);

// 响应式数据
const loading = ref(true);
const error = ref('');
const iframeRef = ref(null);

// 方法
const handleBack = () => {
    emit('back');
};

const handleRefresh = () => {
    if (iframeRef.value) {
        loading.value = true;
        error.value = '';
        iframeRef.value.src = props.url;
    }
};

const handleRetry = () => {
    loading.value = true;
    error.value = '';
    if (iframeRef.value) {
        iframeRef.value.src = props.url;
    }
};

const handleIframeLoad = () => {
    loading.value = false;
    error.value = '';
    emit('load');
};

const handleIframeError = () => {
    loading.value = false;
    error.value = '页面加载失败，请检查网络连接或稍后重试';
    emit('error', error.value);
};

// 监听 URL 变化
watch(() => props.url, (newUrl) => {
    if (newUrl && iframeRef.value) {
        loading.value = true;
        error.value = '';
        iframeRef.value.src = newUrl;
    }
});

// 生命周期
onMounted(() => {
    // 设置加载超时
    const loadTimeout = setTimeout(() => {
        if (loading.value) {
            error.value = '页面加载超时，请重试';
            loading.value = false;
        }
    }, 30000); // 30秒超时

    onBeforeUnmount(() => {
        clearTimeout(loadTimeout);
    });
});
</script>

<style lang="scss" scoped>
.iframe-viewer {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #f5f5f5;
}

.iframe-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    background-color: #fff;
    border-bottom: 1px solid #e0e0e0;
    padding: 0 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.header-left,
.header-right {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.back-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    background: none;
    border: none;
    color: #007aff;
    font-size: 16px;
    cursor: pointer;
    border-radius: 6px;
    transition: background-color 0.2s;

    &:hover {
        background-color: #f0f0f0;
    }

    &:active {
        background-color: #e0e0e0;
    }

    i {
        font-size: 14px;
    }

    span {
        font-size: 16px;
    }
}

.page-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.refresh-btn {
    padding: 8px;
    background: none;
    border: none;
    color: #007aff;
    font-size: 18px;
    cursor: pointer;
    border-radius: 6px;
    transition: background-color 0.2s;

    &:hover:not(:disabled) {
        background-color: #f0f0f0;
    }

    &:active:not(:disabled) {
        background-color: #e0e0e0;
    }

    &:disabled {
        color: #ccc;
        cursor: not-allowed;
    }

    .spinning {
        animation: spin 1s linear infinite;
    }
}

.loading-container,
.error-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: #666;

    i {
        font-size: 24px;
        color: #007aff;
    }

    span {
        font-size: 14px;
    }
}

.error-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 32px;
    text-align: center;
    color: #666;

    i {
        font-size: 48px;
        color: #ff6b6b;
    }

    h3 {
        margin: 0;
        font-size: 18px;
        color: #333;
    }

    p {
        margin: 0;
        font-size: 14px;
        line-height: 1.5;
        max-width: 300px;
    }
}

.retry-btn {
    padding: 10px 20px;
    background-color: #007aff;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
        background-color: #0056cc;
    }

    &:active {
        background-color: #004499;
    }
}

.iframe-container {
    flex: 1;
    position: relative;
    background-color: #fff;
}

.content-iframe {
    width: 100%;
    height: 100%;
    border: none;
    display: block;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

// 响应式设计
@media (max-width: 768px) {
    .iframe-header {
        height: 44px;
        padding: 0 12px;
    }

    .page-title {
        font-size: 14px;
        max-width: 150px;
    }

    .back-btn {
        padding: 6px 8px;
        
        span {
            font-size: 14px;
        }
    }

    .refresh-btn {
        padding: 6px;
        font-size: 16px;
    }
}
</style>
